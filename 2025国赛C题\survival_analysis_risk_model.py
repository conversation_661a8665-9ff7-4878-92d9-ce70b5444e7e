import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from lifelines import <PERSON><PERSON>eierFitter, CoxPHFitter, WeibullFitter
from lifelines.statistics import logrank_test
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SurvivalBasedRiskModel:
    """基于生存分析的产前检测风险管理模型"""
    
    def __init__(self, detection_threshold=0.04):
        self.detection_threshold = detection_threshold
        self.km_fitters = {}
        self.cox_model = None
        self.weibull_fitters = {}
        self.state_transition_probs = {}
        
    def prepare_survival_data(self, df):
        """准备生存分析数据"""
        # 数据预处理
        df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
        
        # 定义BMI分组
        def get_bmi_group(bmi):
            if 20 <= bmi < 28: return '[20,28)'
            elif 28 <= bmi < 32: return '[28,32)'
            elif 32 <= bmi < 36: return '[32,36)'
            elif 36 <= bmi < 40: return '[36,40)'
            elif bmi >= 40: return '≥40'
            else: return '<20'
        
        df_clean = df_clean.copy()
        df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)
        
        # 为每个孕妇构建生存数据
        survival_data = []
        
        for patient_id, group in df_clean.groupby('孕妇代码'):
            group = group.sort_values('检测孕周')
            
            # 找到首次达标时间（事件发生时间）
            success_mask = group['Y染色体浓度'] > self.detection_threshold
            
            if success_mask.any():
                # 事件发生：找到首次达标时间
                event_time = group[success_mask]['检测孕周'].iloc[0]
                event_occurred = 1
                censored = 0
            else:
                # 事件未发生：使用最后观测时间
                event_time = group['检测孕周'].iloc[-1]
                event_occurred = 0
                censored = 1
            
            # 获取患者基本信息
            patient_info = group.iloc[0]
            
            survival_data.append({
                '孕妇代码': patient_id,
                '时间': event_time,  # 生存时间（到达标的时间）
                '事件': event_occurred,  # 是否达标
                '删失': censored,  # 是否被删失
                'BMI组': patient_info['BMI组'],
                '孕妇BMI': patient_info['孕妇BMI'],
                '年龄': patient_info['年龄'],
                '身高': patient_info['身高'],
                '体重': patient_info['体重'],
                'GC含量': patient_info.get('GC含量', np.nan),
                '原始读段数': patient_info.get('原始读段数', np.nan)
            })
        
        return pd.DataFrame(survival_data)
    
    def fit_kaplan_meier(self, survival_df):
        """拟合Kaplan-Meier生存曲线"""
        print("拟合Kaplan-Meier生存曲线...")
        
        # 为每个BMI组拟合KM曲线
        for bmi_group in survival_df['BMI组'].unique():
            if bmi_group == '<20':
                continue
                
            group_data = survival_df[survival_df['BMI组'] == bmi_group]
            if len(group_data) < 10:
                continue
                
            kmf = KaplanMeierFitter()
            kmf.fit(group_data['时间'], group_data['事件'], label=bmi_group)
            self.km_fitters[bmi_group] = kmf
            
        return self.km_fitters
    
    def fit_cox_model(self, survival_df):
        """拟合Cox比例风险模型"""
        print("拟合Cox比例风险模型...")
        
        # 准备协变量
        cox_data = survival_df.copy()
        
        # 创建BMI组的哑变量
        bmi_dummies = pd.get_dummies(cox_data['BMI组'], prefix='BMI')
        cox_data = pd.concat([cox_data, bmi_dummies], axis=1)
        
        # 选择协变量
        covariates = ['孕妇BMI', '年龄']
        
        # 添加BMI组哑变量（除了参考组）
        bmi_cols = [col for col in bmi_dummies.columns if col != 'BMI_[20,28)']
        covariates.extend(bmi_cols)
        
        # 删除缺失值
        cox_data_clean = cox_data[['时间', '事件'] + covariates].dropna()
        
        if len(cox_data_clean) > 0:
            # 拟合Cox模型
            cph = CoxPHFitter()
            cph.fit(cox_data_clean, duration_col='时间', event_col='事件')
            self.cox_model = cph
            
            print("Cox模型系数:")
            print(cph.summary)
            
        return self.cox_model
    
    def calculate_hazard_ratios(self, patient_features):
        """计算个体风险比"""
        if self.cox_model is None:
            return None
            
        # 预测个体风险
        hazard_ratio = self.cox_model.predict_partial_hazard(patient_features)
        survival_function = self.cox_model.predict_survival_function(patient_features)
        
        return {
            'hazard_ratio': hazard_ratio,
            'survival_function': survival_function
        }
    
    def estimate_optimal_detection_times(self, survival_df, target_success_rate=0.8):
        """基于生存分析估计最优检测时间"""
        optimal_times = {}
        
        for bmi_group, kmf in self.km_fitters.items():
            # 找到达到目标成功率的时间点
            survival_probs = kmf.survival_function_
            
            # 成功率 = 1 - 生存概率（在生存分析中，"生存"意味着还未达标）
            success_rates = 1 - survival_probs[bmi_group]
            
            # 找到首次达到目标成功率的时间
            target_times = success_rates[success_rates >= target_success_rate]
            
            if len(target_times) > 0:
                optimal_time = target_times.index[0]
                actual_success_rate = target_times.iloc[0]
            else:
                # 如果无法达到目标成功率，选择最高成功率对应的时间
                max_success_idx = success_rates.idxmax()
                optimal_time = max_success_idx
                actual_success_rate = success_rates.loc[max_success_idx]
            
            # 计算置信区间
            confidence_interval = kmf.confidence_interval_survival_function_
            ci_lower = 1 - confidence_interval.loc[optimal_time, f'{bmi_group}_upper_0.95']
            ci_upper = 1 - confidence_interval.loc[optimal_time, f'{bmi_group}_lower_0.95']
            
            optimal_times[bmi_group] = {
                '最优检测时间': optimal_time,
                '预期成功率': actual_success_rate,
                '置信区间下限': ci_lower,
                '置信区间上限': ci_upper,
                '样本数': len(survival_df[survival_df['BMI组'] == bmi_group])
            }
            
        return optimal_times
    
    def model_state_transitions(self, df):
        """建模状态转移过程"""
        print("建模状态转移过程...")
        
        # 定义状态：
        # 0: 未检测
        # 1: 检测未达标
        # 2: 检测达标
        
        df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
        df_clean['BMI组'] = df_clean['孕妇BMI'].apply(lambda x: 
            '[20,28)' if 20 <= x < 28 else
            '[28,32)' if 28 <= x < 32 else
            '[32,36)' if 32 <= x < 36 else
            '[36,40)' if 36 <= x < 40 else
            '≥40' if x >= 40 else '<20')
        
        state_transitions = {}
        
        for bmi_group in df_clean['BMI组'].unique():
            if bmi_group == '<20':
                continue
                
            group_data = df_clean[df_clean['BMI组'] == bmi_group]
            
            # 按时间窗口计算转移概率
            time_windows = [(10, 12), (12, 14), (14, 16), (16, 18), (18, 20), (20, 22), (22, 24)]
            transitions = []
            
            for i, (start_week, end_week) in enumerate(time_windows):
                window_data = group_data[
                    (group_data['检测孕周'] >= start_week) & 
                    (group_data['检测孕周'] < end_week)
                ]
                
                if len(window_data) > 0:
                    success_rate = (window_data['Y染色体浓度'] > self.detection_threshold).mean()
                    failure_rate = 1 - success_rate
                    
                    transitions.append({
                        '时间窗口': f'{start_week}-{end_week}周',
                        '成功转移概率': success_rate,
                        '失败转移概率': failure_rate,
                        '样本数': len(window_data)
                    })
            
            state_transitions[bmi_group] = transitions
            
        self.state_transition_probs = state_transitions
        return state_transitions
    
    def calculate_cumulative_success_probability(self, bmi_group, max_attempts=3):
        """计算多次检测的累积成功概率"""
        if bmi_group not in self.state_transition_probs:
            return None
            
        transitions = self.state_transition_probs[bmi_group]
        
        # 计算多次尝试的累积成功概率
        cumulative_probs = []
        
        for attempt in range(1, max_attempts + 1):
            if attempt == 1:
                # 第一次尝试
                prob = transitions[0]['成功转移概率'] if transitions else 0
            else:
                # 多次尝试：P(至少一次成功) = 1 - P(全部失败)
                failure_prob = 1
                for i in range(min(attempt, len(transitions))):
                    failure_prob *= transitions[i]['失败转移概率']
                prob = 1 - failure_prob
            
            cumulative_probs.append({
                '尝试次数': attempt,
                '累积成功概率': prob
            })
        
        return cumulative_probs
    
    def generate_risk_based_strategy(self, survival_df, risk_tolerance=0.1):
        """生成基于风险的检测策略"""
        print("\n生成基于风险的检测策略...")
        
        strategies = {}
        
        for bmi_group in self.km_fitters.keys():
            kmf = self.km_fitters[bmi_group]
            
            # 基于生存曲线制定策略
            survival_func = kmf.survival_function_[bmi_group]
            
            # 找到风险可接受的时间点
            # 风险 = 1 - 成功率 = 生存概率
            acceptable_times = survival_func[survival_func <= risk_tolerance]
            
            if len(acceptable_times) > 0:
                recommended_time = acceptable_times.index[0]
                risk_level = acceptable_times.iloc[0]
            else:
                # 如果无法达到风险容忍度，选择风险最低的时间点
                min_risk_time = survival_func.idxmin()
                recommended_time = min_risk_time
                risk_level = survival_func.loc[min_risk_time]
            
            # 计算多次检测策略
            cumulative_probs = self.calculate_cumulative_success_probability(bmi_group)
            
            strategies[bmi_group] = {
                '推荐检测时间': recommended_time,
                '单次成功率': 1 - risk_level,
                '风险水平': risk_level,
                '多次检测累积概率': cumulative_probs,
                '样本数': len(survival_df[survival_df['BMI组'] == bmi_group])
            }
        
        return strategies
    
    def visualize_survival_analysis(self, survival_df):
        """可视化生存分析结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('基于生存分析的产前检测风险评估', fontsize=16)
        
        # 1. Kaplan-Meier生存曲线
        ax1 = axes[0, 0]
        for bmi_group, kmf in self.km_fitters.items():
            kmf.plot_survival_function(ax=ax1, label=bmi_group)
        ax1.set_title('各BMI组生存曲线（未达标概率）')
        ax1.set_xlabel('孕周')
        ax1.set_ylabel('未达标概率')
        ax1.legend()
        
        # 2. 成功率曲线（1-生存概率）
        ax2 = axes[0, 1]
        for bmi_group, kmf in self.km_fitters.items():
            success_rates = 1 - kmf.survival_function_[bmi_group]
            ax2.plot(success_rates.index, success_rates.values, label=bmi_group, marker='o')
        ax2.set_title('各BMI组检测成功率曲线')
        ax2.set_xlabel('孕周')
        ax2.set_ylabel('检测成功率')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 风险比可视化（如果有Cox模型）
        if self.cox_model is not None:
            ax3 = axes[1, 0]
            hazard_ratios = self.cox_model.summary['exp(coef)']
            hazard_ratios.plot(kind='barh', ax=ax3)
            ax3.set_title('Cox模型风险比')
            ax3.set_xlabel('风险比 (Hazard Ratio)')
        
        # 4. 状态转移概率热图
        ax4 = axes[1, 1]
        if self.state_transition_probs:
            try:
                # 创建转移概率矩阵用于热图
                bmi_groups = list(self.state_transition_probs.keys())
                if bmi_groups and self.state_transition_probs[bmi_groups[0]]:
                    time_windows = [t['时间窗口'] for t in self.state_transition_probs[bmi_groups[0]]]

                    # 确保所有BMI组都有相同数量的时间窗口
                    min_windows = min(len(self.state_transition_probs[group]) for group in bmi_groups)
                    time_windows = time_windows[:min_windows]

                    prob_matrix = []
                    for bmi_group in bmi_groups:
                        probs = [t['成功转移概率'] for t in self.state_transition_probs[bmi_group][:min_windows]]
                        prob_matrix.append(probs)

                    if prob_matrix and all(len(row) == len(prob_matrix[0]) for row in prob_matrix):
                        sns.heatmap(prob_matrix,
                                   xticklabels=time_windows,
                                   yticklabels=bmi_groups,
                                   annot=True,
                                   fmt='.2f',
                                   cmap='RdYlGn',
                                   ax=ax4)
                        ax4.set_title('各时间窗口成功转移概率')
                    else:
                        ax4.text(0.5, 0.5, '数据不足以生成热图',
                                ha='center', va='center', transform=ax4.transAxes)
                        ax4.set_title('状态转移概率')
                else:
                    ax4.text(0.5, 0.5, '无状态转移数据',
                            ha='center', va='center', transform=ax4.transAxes)
                    ax4.set_title('状态转移概率')
            except Exception as e:
                ax4.text(0.5, 0.5, f'热图生成错误: {str(e)}',
                        ha='center', va='center', transform=ax4.transAxes)
                ax4.set_title('状态转移概率')
        
        plt.tight_layout()
        plt.show()

def main():
    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")
    
    # 创建生存分析模型
    survival_model = SurvivalBasedRiskModel(detection_threshold=0.04)
    
    # 准备生存数据
    survival_df = survival_model.prepare_survival_data(df)
    print(f"生存分析数据形状: {survival_df.shape}")
    print(f"事件发生率: {survival_df['事件'].mean():.2%}")
    
    # 拟合Kaplan-Meier模型
    km_fitters = survival_model.fit_kaplan_meier(survival_df)
    
    # 拟合Cox比例风险模型
    cox_model = survival_model.fit_cox_model(survival_df)
    
    # 建模状态转移
    state_transitions = survival_model.model_state_transitions(df)
    
    # 估计最优检测时间
    optimal_times = survival_model.estimate_optimal_detection_times(survival_df, target_success_rate=0.8)
    
    # 生成风险策略
    risk_strategies = survival_model.generate_risk_based_strategy(survival_df, risk_tolerance=0.2)
    
    return survival_model, survival_df, optimal_times, risk_strategies

def print_survival_based_recommendations(optimal_times, strategies):
    """打印基于生存分析的建议"""
    print("\n" + "="*80)
    print("基于生存分析的产前检测策略")
    print("="*80)

    print("\n【最优检测时间分析】")
    for bmi_group, info in optimal_times.items():
        print(f"\n{bmi_group} BMI组:")
        print(f"  最优检测时间: {info['最优检测时间']:.1f}周")
        print(f"  预期成功率: {info['预期成功率']:.2%}")
        print(f"  95%置信区间: [{info['置信区间下限']:.2%}, {info['置信区间上限']:.2%}]")
        print(f"  样本数: {info['样本数']}")

    print("\n【风险导向策略】")
    for bmi_group, strategy in strategies.items():
        print(f"\n{bmi_group} BMI组:")
        print(f"  推荐检测时间: {strategy['推荐检测时间']:.1f}周")
        print(f"  单次成功率: {strategy['单次成功率']:.2%}")
        print(f"  风险水平: {strategy['风险水平']:.2%}")

        if strategy['多次检测累积概率']:
            print(f"  多次检测策略:")
            for prob_info in strategy['多次检测累积概率']:
                print(f"    {prob_info['尝试次数']}次检测累积成功率: {prob_info['累积成功概率']:.2%}")

class MarkovChainRiskModel:
    """基于马尔可夫链的状态转移风险模型"""

    def __init__(self, states=['未检测', '检测失败', '检测成功'], detection_threshold=0.04):
        self.states = states
        self.detection_threshold = detection_threshold
        self.transition_matrices = {}
        self.steady_state_probs = {}

    def build_transition_matrix(self, df, bmi_group, time_window_size=2):
        """构建状态转移矩阵"""
        group_data = df[df['BMI组'] == bmi_group].copy()

        # 按患者分组，构建状态序列
        patient_states = {}

        for patient_id, patient_data in group_data.groupby('孕妇代码'):
            patient_data = patient_data.sort_values('检测孕周')
            states_sequence = []

            for _, row in patient_data.iterrows():
                if row['Y染色体浓度'] > self.detection_threshold:
                    states_sequence.append('检测成功')
                else:
                    states_sequence.append('检测失败')

            patient_states[patient_id] = states_sequence

        # 统计状态转移
        transitions = {
            ('未检测', '检测失败'): 0,
            ('未检测', '检测成功'): 0,
            ('检测失败', '检测失败'): 0,
            ('检测失败', '检测成功'): 0,
            ('检测成功', '检测成功'): 0  # 吸收状态
        }

        total_transitions = {state: 0 for state in self.states}

        for patient_id, sequence in patient_states.items():
            # 第一次检测
            if sequence:
                if sequence[0] == '检测成功':
                    transitions[('未检测', '检测成功')] += 1
                else:
                    transitions[('未检测', '检测失败')] += 1
                total_transitions['未检测'] += 1

            # 后续转移
            for i in range(len(sequence) - 1):
                current_state = sequence[i]
                next_state = sequence[i + 1]

                if (current_state, next_state) in transitions:
                    transitions[(current_state, next_state)] += 1
                    total_transitions[current_state] += 1

        # 构建转移概率矩阵
        n_states = len(self.states)
        transition_matrix = np.zeros((n_states, n_states))

        state_to_idx = {state: i for i, state in enumerate(self.states)}

        for (from_state, to_state), count in transitions.items():
            if total_transitions[from_state] > 0:
                prob = count / total_transitions[from_state]
                i = state_to_idx[from_state]
                j = state_to_idx[to_state]
                transition_matrix[i, j] = prob

        # 检测成功是吸收状态
        success_idx = state_to_idx['检测成功']
        transition_matrix[success_idx, success_idx] = 1.0

        self.transition_matrices[bmi_group] = transition_matrix
        return transition_matrix

    def calculate_absorption_probabilities(self, bmi_group, max_steps=10):
        """计算吸收概率（最终检测成功的概率）"""
        if bmi_group not in self.transition_matrices:
            return None

        P = self.transition_matrices[bmi_group]

        # 分离瞬时状态和吸收状态
        # 假设最后一个状态是吸收状态（检测成功）
        Q = P[:-1, :-1]  # 瞬时状态之间的转移
        R = P[:-1, -1:]  # 瞬时状态到吸收状态的转移

        # 计算基本矩阵 N = (I - Q)^(-1)
        I = np.eye(Q.shape[0])
        try:
            N = np.linalg.inv(I - Q)
            # 吸收概率 B = NR
            B = N @ R

            absorption_probs = {}
            for i, state in enumerate(self.states[:-1]):
                absorption_probs[state] = B[i, 0]

            return absorption_probs, N
        except np.linalg.LinAlgError:
            return None, None

    def simulate_detection_process(self, bmi_group, initial_state='未检测', max_steps=10, n_simulations=1000):
        """蒙特卡洛模拟检测过程"""
        if bmi_group not in self.transition_matrices:
            return None

        P = self.transition_matrices[bmi_group]
        state_to_idx = {state: i for i, state in enumerate(self.states)}
        idx_to_state = {i: state for i, state in enumerate(self.states)}

        simulation_results = []

        for _ in range(n_simulations):
            current_state = initial_state
            current_idx = state_to_idx[current_state]
            steps = 0
            path = [current_state]

            while steps < max_steps and current_state != '检测成功':
                # 根据转移概率选择下一个状态
                probs = P[current_idx, :]
                next_idx = np.random.choice(len(self.states), p=probs)
                current_state = idx_to_state[next_idx]
                current_idx = next_idx
                steps += 1
                path.append(current_state)

            simulation_results.append({
                'steps_to_success': steps if current_state == '检测成功' else max_steps,
                'final_state': current_state,
                'path': path,
                'success': current_state == '检测成功'
            })

        return simulation_results

    def analyze_risk_metrics(self, simulation_results):
        """分析风险指标"""
        if not simulation_results:
            return None

        success_rate = np.mean([r['success'] for r in simulation_results])
        avg_steps = np.mean([r['steps_to_success'] for r in simulation_results if r['success']])

        # 计算不同步数的成功概率
        step_success_probs = {}
        for step in range(1, 11):
            step_success = np.mean([r['success'] and r['steps_to_success'] <= step
                                  for r in simulation_results])
            step_success_probs[step] = step_success

        return {
            'overall_success_rate': success_rate,
            'average_steps_to_success': avg_steps,
            'step_wise_success_probabilities': step_success_probs
        }

def enhanced_main():
    """增强版主函数，包含马尔可夫链分析"""
    # 运行基础生存分析
    model, survival_df, optimal_times, strategies = main()

    # 打印生存分析结果
    print_survival_based_recommendations(optimal_times, strategies)

    # 可视化结果
    model.visualize_survival_analysis(survival_df)

    # 马尔可夫链分析
    print("\n" + "="*80)
    print("马尔可夫链状态转移分析")
    print("="*80)

    # 加载原始数据用于马尔可夫分析
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
    df_clean['BMI组'] = df_clean['孕妇BMI'].apply(lambda x:
        '[20,28)' if 20 <= x < 28 else
        '[28,32)' if 28 <= x < 32 else
        '[32,36)' if 32 <= x < 36 else
        '[36,40)' if 36 <= x < 40 else
        '≥40' if x >= 40 else '<20')

    markov_model = MarkovChainRiskModel()

    for bmi_group in ['[28,32)', '[32,36)', '[36,40)']:
        if bmi_group in df_clean['BMI组'].values:
            print(f"\n【{bmi_group} BMI组马尔可夫分析】")

            # 构建转移矩阵
            transition_matrix = markov_model.build_transition_matrix(df_clean, bmi_group)
            print("状态转移矩阵:")
            print(pd.DataFrame(transition_matrix,
                             index=markov_model.states,
                             columns=markov_model.states))

            # 计算吸收概率
            absorption_probs, N = markov_model.calculate_absorption_probabilities(bmi_group)
            if absorption_probs:
                print("\n最终检测成功概率:")
                for state, prob in absorption_probs.items():
                    print(f"  从{state}开始: {prob:.2%}")

            # 蒙特卡洛模拟
            simulation_results = markov_model.simulate_detection_process(bmi_group)
            if simulation_results:
                risk_metrics = markov_model.analyze_risk_metrics(simulation_results)
                print(f"\n模拟结果 (基于{len(simulation_results)}次模拟):")
                print(f"  总体成功率: {risk_metrics['overall_success_rate']:.2%}")
                print(f"  平均成功步数: {risk_metrics['average_steps_to_success']:.1f}")

                print("  不同检测次数的累积成功率:")
                for step, prob in risk_metrics['step_wise_success_probabilities'].items():
                    if step <= 5:  # 只显示前5次
                        print(f"    {step}次检测: {prob:.2%}")

    return model, markov_model, optimal_times, strategies

if __name__ == '__main__':
    model, markov_model, optimal_times, strategies = enhanced_main()
