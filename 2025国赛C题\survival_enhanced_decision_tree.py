import pandas as pd
import numpy as np
from sklearn.tree import DecisionTreeRegressor, DecisionTreeClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
from lifelines import KaplanMeierFitter
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SurvivalEnhancedDecisionTree:
    """融合生存分析信息的增强决策树"""
    
    def __init__(self, detection_threshold=0.04, time_points=None):
        self.detection_threshold = detection_threshold
        self.time_points = time_points or np.arange(10, 25, 0.5)  # 检测时间点
        self.km_fitters = {}
        self.survival_features = {}
        self.success_rate_features = {}
        self.enhanced_models = {}
        
    def extract_survival_features(self, df):
        """从生存曲线中提取特征"""
        print("提取生存分析特征...")
        
        # 准备生存数据
        df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
        
        def get_bmi_group(bmi):
            if 20 <= bmi < 28: return '[20,28)'
            elif 28 <= bmi < 32: return '[28,32)'
            elif 32 <= bmi < 36: return '[32,36)'
            elif 36 <= bmi < 40: return '[36,40)'
            elif bmi >= 40: return '≥40'
            else: return '<20'
        
        df_clean = df_clean.copy()
        df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)
        
        # 为每个BMI组构建生存数据和拟合KM曲线
        for bmi_group in df_clean['BMI组'].unique():
            if bmi_group == '<20':
                continue
                
            group_data = df_clean[df_clean['BMI组'] == bmi_group]
            if len(group_data) < 20:
                continue
            
            # 构建生存数据
            survival_data = []
            for patient_id, patient_group in group_data.groupby('孕妇代码'):
                patient_group = patient_group.sort_values('检测孕周')
                
                # 找到首次达标时间
                success_mask = patient_group['Y染色体浓度'] > self.detection_threshold
                if success_mask.any():
                    event_time = patient_group[success_mask]['检测孕周'].iloc[0]
                    event_occurred = 1
                else:
                    event_time = patient_group['检测孕周'].iloc[-1]
                    event_occurred = 0
                
                survival_data.append({
                    'time': event_time,
                    'event': event_occurred
                })
            
            survival_df = pd.DataFrame(survival_data)
            
            # 拟合KM曲线
            kmf = KaplanMeierFitter()
            kmf.fit(survival_df['time'], survival_df['event'])
            self.km_fitters[bmi_group] = kmf
            
            # 提取生存特征和成功率特征
            survival_func = kmf.survival_function_.iloc[:, 0]
            success_rates = 1 - survival_func  # 成功率 = 1 - 生存概率
            
            # 插值到标准时间点
            time_index = survival_func.index
            
            # 生存概率插值
            if len(time_index) > 1:
                survival_interp = interp1d(time_index, survival_func.values, 
                                         kind='linear', bounds_error=False, 
                                         fill_value=(survival_func.iloc[0], survival_func.iloc[-1]))
                survival_at_timepoints = survival_interp(self.time_points)
            else:
                survival_at_timepoints = np.full(len(self.time_points), survival_func.iloc[0])
            
            # 成功率插值
            if len(time_index) > 1:
                success_interp = interp1d(time_index, success_rates.values,
                                        kind='linear', bounds_error=False,
                                        fill_value=(success_rates.iloc[0], success_rates.iloc[-1]))
                success_at_timepoints = success_interp(self.time_points)
            else:
                success_at_timepoints = np.full(len(self.time_points), success_rates.iloc[0])
            
            # 存储特征
            self.survival_features[bmi_group] = survival_at_timepoints
            self.success_rate_features[bmi_group] = success_at_timepoints
            
        return self.survival_features, self.success_rate_features
    
    def create_enhanced_features(self, df):
        """创建增强特征集"""
        print("创建增强特征集...")
        
        df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
        df_clean['BMI组'] = df_clean['孕妇BMI'].apply(lambda x: 
            '[20,28)' if 20 <= x < 28 else
            '[28,32)' if 28 <= x < 32 else
            '[32,36)' if 32 <= x < 36 else
            '[36,40)' if 36 <= x < 40 else
            '≥40' if x >= 40 else '<20')
        
        enhanced_data = []
        
        for _, row in df_clean.iterrows():
            bmi_group = row['BMI组']
            detection_week = row['检测孕周']
            
            if bmi_group in self.survival_features:
                # 找到最接近的时间点
                time_idx = np.argmin(np.abs(self.time_points - detection_week))
                
                # 基础特征
                features = {
                    '孕妇BMI': row['孕妇BMI'],
                    '检测孕周': detection_week,
                    '年龄': row['年龄'],
                    'Y染色体浓度': row['Y染色体浓度'],
                    '是否达标': 1 if row['Y染色体浓度'] > self.detection_threshold else 0
                }
                
                # 生存分析增强特征
                features.update({
                    f'生存概率_当前时点': self.survival_features[bmi_group][time_idx],
                    f'成功率_当前时点': self.success_rate_features[bmi_group][time_idx],
                    f'生存概率_早期': np.mean(self.survival_features[bmi_group][:5]),  # 前5个时间点平均
                    f'生存概率_中期': np.mean(self.survival_features[bmi_group][5:10]),
                    f'生存概率_晚期': np.mean(self.survival_features[bmi_group][10:]),
                    f'成功率_早期': np.mean(self.success_rate_features[bmi_group][:5]),
                    f'成功率_中期': np.mean(self.success_rate_features[bmi_group][5:10]),
                    f'成功率_晚期': np.mean(self.success_rate_features[bmi_group][10:]),
                    f'成功率_趋势': self.success_rate_features[bmi_group][-1] - self.success_rate_features[bmi_group][0],  # 成功率变化趋势
                    f'风险梯度': np.gradient(self.survival_features[bmi_group])[time_idx],  # 当前时点的风险变化率
                })
                
                # BMI组编码
                for group in ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']:
                    features[f'BMI_{group}'] = 1 if bmi_group == group else 0
                
                enhanced_data.append(features)
        
        return pd.DataFrame(enhanced_data)
    
    def train_enhanced_models(self, enhanced_df):
        """训练增强决策树模型"""
        print("训练增强决策树模型...")
        
        # 准备特征和目标变量
        feature_cols = [col for col in enhanced_df.columns 
                       if col not in ['Y染色体浓度', '是否达标']]
        
        X = enhanced_df[feature_cols]
        
        # 模型1: 预测Y染色体浓度（回归）
        y_concentration = enhanced_df['Y染色体浓度']
        X_train, X_test, y_train, y_test = train_test_split(X, y_concentration, 
                                                           test_size=0.3, random_state=42)
        
        concentration_model = DecisionTreeRegressor(max_depth=10, min_samples_split=10, 
                                                   min_samples_leaf=5, random_state=42)
        concentration_model.fit(X_train, y_train)
        
        y_pred = concentration_model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"浓度预测模型 - MSE: {mse:.6f}, R²: {r2:.4f}")
        
        # 模型2: 预测是否达标（分类）
        y_success = enhanced_df['是否达标']
        X_train, X_test, y_train, y_test = train_test_split(X, y_success, 
                                                           test_size=0.3, random_state=42)
        
        success_model = DecisionTreeClassifier(max_depth=10, min_samples_split=10,
                                             min_samples_leaf=5, random_state=42)
        success_model.fit(X_train, y_train)
        
        y_pred = success_model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"达标预测模型 - 准确率: {accuracy:.4f}")
        
        # 特征重要性分析
        feature_importance = pd.DataFrame({
            '特征': feature_cols,
            '浓度预测重要性': concentration_model.feature_importances_,
            '达标预测重要性': success_model.feature_importances_
        }).sort_values('浓度预测重要性', ascending=False)
        
        print("\n特征重要性排序（前10个）:")
        print(feature_importance.head(10))
        
        self.enhanced_models = {
            'concentration': concentration_model,
            'success': success_model,
            'feature_importance': feature_importance,
            'feature_cols': feature_cols
        }
        
        return self.enhanced_models
    
    def predict_optimal_detection_strategy(self, patient_bmi, patient_age=30):
        """为个体患者预测最优检测策略"""
        if not self.enhanced_models:
            print("请先训练模型")
            return None
        
        # 确定BMI组
        if 20 <= patient_bmi < 28: bmi_group = '[20,28)'
        elif 28 <= patient_bmi < 32: bmi_group = '[28,32)'
        elif 32 <= patient_bmi < 36: bmi_group = '[32,36)'
        elif 36 <= patient_bmi < 40: bmi_group = '[36,40)'
        elif patient_bmi >= 40: bmi_group = '≥40'
        else: bmi_group = '<20'
        
        if bmi_group not in self.survival_features:
            print(f"BMI组 {bmi_group} 数据不足")
            return None
        
        # 评估不同时间点的预测结果
        results = []
        concentration_model = self.enhanced_models['concentration']
        success_model = self.enhanced_models['success']
        feature_cols = self.enhanced_models['feature_cols']
        
        for i, detection_week in enumerate(self.time_points):
            # 构建特征向量
            features = {
                '孕妇BMI': patient_bmi,
                '检测孕周': detection_week,
                '年龄': patient_age,
                f'生存概率_当前时点': self.survival_features[bmi_group][i],
                f'成功率_当前时点': self.success_rate_features[bmi_group][i],
                f'生存概率_早期': np.mean(self.survival_features[bmi_group][:5]),
                f'生存概率_中期': np.mean(self.survival_features[bmi_group][5:10]),
                f'生存概率_晚期': np.mean(self.survival_features[bmi_group][10:]),
                f'成功率_早期': np.mean(self.success_rate_features[bmi_group][:5]),
                f'成功率_中期': np.mean(self.success_rate_features[bmi_group][5:10]),
                f'成功率_晚期': np.mean(self.success_rate_features[bmi_group][10:]),
                f'成功率_趋势': self.success_rate_features[bmi_group][-1] - self.success_rate_features[bmi_group][0],
                f'风险梯度': np.gradient(self.survival_features[bmi_group])[i],
            }
            
            # BMI组编码
            for group in ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']:
                features[f'BMI_{group}'] = 1 if bmi_group == group else 0
            
            # 转换为DataFrame
            feature_df = pd.DataFrame([features])
            
            # 确保特征顺序一致
            feature_df = feature_df.reindex(columns=feature_cols, fill_value=0)
            
            # 预测
            pred_concentration = concentration_model.predict(feature_df)[0]
            pred_success_prob = success_model.predict_proba(feature_df)[0][1]
            
            results.append({
                '检测时间': detection_week,
                '预测浓度': pred_concentration,
                '成功概率': pred_success_prob,
                '生存概率': self.survival_features[bmi_group][i],
                '历史成功率': self.success_rate_features[bmi_group][i]
            })
        
        results_df = pd.DataFrame(results)
        
        # 找到最优检测时间
        # 综合考虑成功概率和风险
        results_df['综合评分'] = (results_df['成功概率'] * 0.6 + 
                              results_df['历史成功率'] * 0.3 + 
                              (1 - results_df['生存概率']) * 0.1)
        
        optimal_idx = results_df['综合评分'].idxmax()
        optimal_result = results_df.iloc[optimal_idx]
        
        return {
            'patient_info': {'BMI': patient_bmi, 'BMI组': bmi_group, '年龄': patient_age},
            'optimal_time': optimal_result['检测时间'],
            'predicted_concentration': optimal_result['预测浓度'],
            'success_probability': optimal_result['成功概率'],
            'comprehensive_score': optimal_result['综合评分'],
            'all_predictions': results_df
        }

def main():
    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")
    
    # 创建增强决策树模型
    enhanced_dt = SurvivalEnhancedDecisionTree(detection_threshold=0.04)
    
    # 提取生存分析特征
    survival_features, success_features = enhanced_dt.extract_survival_features(df)
    
    # 创建增强特征集
    enhanced_df = enhanced_dt.create_enhanced_features(df)
    print(f"增强特征数据形状: {enhanced_df.shape}")
    
    # 训练增强模型
    models = enhanced_dt.train_enhanced_models(enhanced_df)
    
    return enhanced_dt, enhanced_df, models

if __name__ == '__main__':
    enhanced_dt, enhanced_df, models = main()
